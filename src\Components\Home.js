import React,{useEffect,useRef,useState} from 'react'
import Chart from'chart.js/auto';
import VariationsBar from './VariationsBar';

function Home() {
  const [products, setProducts] = useState([]);
  const [orders, setOrders] = useState([]);


  const chartRef = useRef(null);
  const lineRef = useRef(null);

  useEffect(() => {
    const token = localStorage.getItem("access_token");
    fetch("http://192.168.100.118:8000/api/products/", {
      headers: {
        'Authorization': `Bearer ${token}`
      },
    })
      .then(res => res.json())
      .then(data => {
        let arr = [];
        if (Array.isArray(data)) arr = data;
        else if (data && Array.isArray(data.results)) arr = data.results;
        setProducts(arr);
        console.log('Fetched products:', arr);
      })
      .catch(() => setProducts([]));
  }, []);

  useEffect(() => {
  const token = localStorage.getItem("access_token");
    fetch("http://192.168.100.118:8000/api/orders/", {
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {})
      }
    })
      .then((res) => {
        if (!res.ok) throw new Error("Unauthorized or error fetching orders");
        return res.json();
      })
      .then((data) => {
        if (Array.isArray(data)) setOrders(data);
        else setOrders([]);
      })
  }, []);



useEffect(() => {
  if (!orders.length || !lineRef.current) return;
  const pdx = lineRef.current.getContext('2d');
  let lineInstance = new Chart(pdx, {
    type: 'line',
    data: {
      labels: orders.map(order => order.created_at),
      datasets: [{
        label: 'Price earned',
        data: orders.map(order => order.total_amount),
        fill: false,
        borderColor: 'rgba(52, 68, 169, 1)',
        tension: 0.1
      }]
    },
  });
  return () => {
    lineInstance.destroy();
  };
}, [orders]);

  useEffect(() => {
    if (!products.length || !chartRef.current) return;
    const ctx = chartRef.current.getContext('2d');
    let chartInstance = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: products.map(product => product.name || product.title || product.id),
        datasets: [{
          label: 'Stocks',
          data: products.map(product => product.stock),
          backgroundColor: 'rgba(37, 99, 235, 0.5)',
          borderColor: 'rgba(37, 99, 235, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: { display: true },
        },
        scales: {
          y: { beginAtZero: true }
        }
      }
    });
    return () => {
      chartInstance.destroy();
    };
  }, [products]);

  
  return (
    <div>
      <div style={{ maxWidth: 800 }}>
        <h1>Products In Stock</h1>
        {products.length === 0 ? (
          <div style={{color: '#888', textAlign: 'center', margin: '2rem 0'}}>No product data to display.</div>
        ) : (
          <canvas ref={chartRef} id="myChart" height={200} style={{ background: '#fff', borderRadius: 8, boxShadow: '0 2px 8px #0001', display: 'block' }}></canvas>
        )}
        <h1>Earning through orders by date</h1>
        {orders.length === 0 ? (
          <div style={{color: '#888', textAlign: 'center', margin: '2rem 0'}}>No order data to display.</div>
        ) : (
          <canvas ref={lineRef} id="ordersChart" height={200} style={{ background: '#fff', borderRadius: 8, boxShadow: '0 2px 8px #0001', display: 'block', marginTop: 32 }}></canvas>
        )}
        <VariationsBar products={products} />
      </div>
    </div>
  )

  
}

export default Home

