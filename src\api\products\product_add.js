
import { useState } from 'react';
import './product_add.css';

function ProductAdd() {

  const[form, setForm] = useState({
    name: '',
    slug: '',
    description: '',
    price: '',
    discount_price: '',
    stock: '',
    sku: '',
    is_available: false,
    is_featured: false,
    meta_title: '',
    meta_description: '',
    tags: '',
    subcategory: '',
    brand: ''
  })

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm({
      ...form,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const token = localStorage.getItem('access_token')
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch("http://***************:8000/api/products/", {
        method: "POST",
        headers: { 'Content-Type': 'application/json' ,
                   'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(form)
      });
      const data = await response.json();
      console.log(data);
    } catch (err) {
      console.error(err);
    }
  };


  return (
    <div className="product-add-container">
      <div className="product-add-title">Add New Product</div>
      <form className="product-add-form" onSubmit={handleSubmit} autoComplete="off">
        <div className="product-add-form-row">
          <label className="product-add-label">Name*</label>
          <input className="product-add-input" type="text" name="name" maxLength={150} minLength={1} required value={form.name} onChange={handleChange} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">Slug</label>
          <input className="product-add-input" type="text" name="slug" maxLength={50} pattern="^[-a-zA-Z0-9_]+$" value={form.slug} onChange={handleChange} />
        </div>
        <div className="product-add-form-row product-add-form-row-full">
          <label className="product-add-label">Description*</label>
          <textarea className="product-add-textarea" name="description" minLength={1} required value={form.description} onChange={handleChange} rows={3} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">Price*</label>
          <input className="product-add-input" type="number" name="price" step="0.01" required value={form.price} onChange={handleChange} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">Discount Price</label>
          <input className="product-add-input" type="number" name="discount_price" step="0.01" value={form.discount_price} onChange={handleChange} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">Stock*</label>
          <input className="product-add-input" type="number" name="stock" min={0} max={9223372036854776000} required value={form.stock} onChange={handleChange} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">SKU*</label>
          <input className="product-add-input" type="text" name="sku" maxLength={50} minLength={1} required value={form.sku} onChange={handleChange} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">Is Available</label>
          <input className="product-add-checkbox" type="checkbox" name="is_available" checked={form.is_available} onChange={handleChange} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">Is Featured</label>
          <input className="product-add-checkbox" type="checkbox" name="is_featured" checked={form.is_featured} onChange={handleChange} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">Meta Title</label>
          <input className="product-add-input" type="text" name="meta_title" maxLength={150} value={form.meta_title} onChange={handleChange} />
        </div>
        <div className="product-add-form-row product-add-form-row-full">
          <label className="product-add-label">Meta Description</label>
          <textarea className="product-add-textarea" name="meta_description" value={form.meta_description} onChange={handleChange} rows={2} />
        </div>
        <div className="product-add-form-row product-add-form-row-full">
          <label className="product-add-label">Tags</label>
          <input className="product-add-input" type="text" name="tags" maxLength={250} value={form.tags} onChange={handleChange} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">Subcategory*</label>
          <input className="product-add-input" type="number" name="subcategory" required value={form.subcategory} onChange={handleChange} />
        </div>
        <div className="product-add-form-row">
          <label className="product-add-label">Brand</label>
          <input className="product-add-input" type="number" name="brand" value={form.brand} onChange={handleChange} />
        </div>
        <div className="product-add-form-row product-add-form-row-full" style={{textAlign: 'center'}}>
          <button className="product-add-submit-btn" type="submit">Add Product</button>
        </div>
      </form>
    </div>
  )
}

export default ProductAdd
