import { useEffect, useState } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import "./product_detail.css";


function ProductDetail() {
  const { id } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const [product, setProduct] = useState(location.state?.product || null);
  const [loading, setLoading] = useState(!product);
  const [error, setError] = useState(null);

  useEffect(() => {
    // If we don't have product data from navigation state, fetch it
    if (!product) {
      const token = localStorage.getItem("access_token");
      fetch(`http://192.168.100.118:8000/api/products/${id}/`, {
        headers: {
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        }
      })
        .then((res) => {
          if (!res.ok) throw new Error("Product not found");
          return res.json();
        })
        .then((data) => setProduct(data))
        .catch((err) => setError(err))
        .finally(() => setLoading(false));
    }
  }, [id, product]);

  const formatPrice = (price, discountPrice) => {
    const originalPrice = parseFloat(price);
    const discount = discountPrice ? parseFloat(discountPrice) : null;
    
    if (discount && discount < originalPrice) {
      return (
        <div className="price-container">
          <span className="price price--current">${discount.toFixed(2)}</span>
          <span className="price price--original">${originalPrice.toFixed(2)}</span>
          <span className="price price--savings">
            Save ${(originalPrice - discount).toFixed(2)}
          </span>
        </div>
      );
    }
    
    return <span className="price price--current">${originalPrice.toFixed(2)}</span>;
  };

  const getStockStatus = (stock, isAvailable) => {
    if (!isAvailable) return { text: "Unavailable", className: "unavailable" };
    if (stock <= 0) return { text: "Out of Stock", className: "out-of-stock" };
    if (stock <= 10) return { text: "Low Stock", className: "low-stock" };
    return { text: "In Stock", className: "in-stock" };
  };

  const getProductImages = (images) => {
    if (!images) return ["https://via.placeholder.com/400x400/f1f5f9/64748b?text=No+Image"];

    if (typeof images === "string") {
      try {
        const imageArray = JSON.parse(images);
        return imageArray.length > 0 ? imageArray : ["https://via.placeholder.com/400x400/f1f5f9/64748b?text=No+Image"];
      } catch {
        return images.startsWith("http") ? [images] : ["https://via.placeholder.com/400x400/f1f5f9/64748b?text=No+Image"];
      }
    }

    if (Array.isArray(images) && images.length > 0) {
      return images;
    }

    return ["https://via.placeholder.com/400x400/f1f5f9/64748b?text=No+Image"];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="product-detail-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading product details...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="product-detail-container">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h2>Product Not Found</h2>
          <p>The requested product could not be found.</p>
          <button 
            className="back-button"
            onClick={() => navigate('/products')}
          >
            Back to Products
          </button>
        </div>
      </div>
    );
  }

  const stockStatus = getStockStatus(product.stock, product.is_available);
  const productImages = getProductImages(product.images);

  return (
    <div className="product-detail-container">
      {/* Breadcrumb Navigation */}
      <nav className="breadcrumb">
        <button 
          className="breadcrumb-link"
          onClick={() => navigate('/products')}
        >
          Products
        </button>
        <span className="breadcrumb-separator">›</span>
        <span className="breadcrumb-current">{product.name}</span>
      </nav>

      <div className="product-detail">
        {/* Product Images */}
        <div className="product-images">
          <div className="main-image-container">
            <img
              src={productImages[0]}
              alt={product.name}
              className="main-image"
              onError={(e) => {
                e.target.src = "https://images.pexels.com/photos/674010/pexels-photo-674010.jpeg";
              }}
            />
            {product.is_featured && (
              <div className="featured-badge">Featured</div>
            )}
          </div>
          
          {productImages.length > 1 && (
            <div className="thumbnail-images">
              {productImages.slice(1, 5).map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={`${product.name} ${index + 2}`}
                  className="thumbnail-image"
                  onError={(e) => {
                    e.target.src = "https://via.placeholder.com/100x100/f1f5f9/64748b?text=No+Image";
                  }}
                />
              ))}
            </div>
          )}
        </div>

        <div className="product-info">
          <div className="product-header">
            <h1 className="product-title">{product.name}</h1>
            <div className="product-sku">SKU: {product.sku}</div>
            <div className={`stock-status stock-status--${stockStatus.className}`}>
              {stockStatus.text}
            </div>
          </div>

          <div className="product-pricing">
            {formatPrice(product.price, product.discount_price)}
          </div>

          <div className="product-description">
            <h3>Description</h3>
            <p>{product.description}</p>
          </div>

          <div className="product-details-grid">
            <div className="detail-item">
              <span className="detail-label">Stock Quantity</span>
              <span className={`detail-value ${product.stock <= 10 ? 'low-stock' : ''}`}>
                {product.stock} units
              </span>
            </div>

            {product.brand && (
              <div className="detail-item">
                <span className="detail-label">Brand</span>
                <span className="detail-value">{product.brand}</span>
              </div>
            )}

            {product.subcategory && (
              <div className="detail-item">
                <span className="detail-label">Category</span>
                <span className="detail-value">{product.subcategory}</span>
              </div>
            )}

            <div className="detail-item">
              <span className="detail-label">Availability</span>
              <span className="detail-value">
                {product.is_available ? "Available" : "Not Available"}
              </span>
            </div>

            <div className="detail-item">
              <span className="detail-label">Date Added</span>
              <span className="detail-value">{formatDate(product.created_at)}</span>
            </div>

            <div className="detail-item">
              <span className="detail-label">Last Updated</span>
              <span className="detail-value">{formatDate(product.updated_at)}</span>
            </div>
          </div>

          {product.tags && (
            <div className="product-tags">
              <h4>Tags</h4>
              <div className="tags-container">
                {product.tags.split(',').map((tag, index) => (
                  <span key={index} className="tag">
                    {tag.trim()}
                  </span>
                ))}
              </div>
            </div>
          )}

          {product.variations && Array.isArray(product.variations) && (
            <div className="product-variations">
              <h4>Variations</h4>
              <ul>
                {product.variations.map((variation) => (
                  <li key={variation.id}>
                    {variation.value} (Additional Price: ${variation.additional_price})
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="product-actions">
            <button 
              className="action-button action-button--primary"
              onClick={() => navigate('/products')}
            >
              Back to Products hehe
            </button>
            <button 
              className="action-button action-button--secondary"
              onClick={() => {
                // Add edit functionality here
                console.log('Edit product:', product.id);
              }}
            >
              Edit Product
            </button>
          </div>
        </div>
      </div>

      {/* SEO Information (if available) */}
      {(product.meta_title || product.meta_description) && (
        <div className="seo-info">
          <h3>SEO Information</h3>
          {product.meta_title && (
            <div className="seo-item">
              <span className="seo-label">Meta Title:</span>
              <span className="seo-value">{product.meta_title}</span>
            </div>
          )}
          {product.meta_description && (
            <div className="seo-item">
              <span className="seo-label">Meta Description:</span>
              <span className="seo-value">{product.meta_description}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ProductDetail;
