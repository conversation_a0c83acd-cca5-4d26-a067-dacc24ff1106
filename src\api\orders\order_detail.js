import { useEffect, useState } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import "./order_detail.css";

function OrderDetail() {
  const { id } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const [order, setOrder] = useState(location.state?.order || null);
  const [loading, setLoading] = useState(!order);
  const [error, setError] = useState(null);

  useEffect(() => {
    // If we don't have order data from navigation state, fetch it
    if (!order) {
      const token = localStorage.getItem("access_token");
      fetch(`http://192.168.100.118:8000/api/orders/${id}/`, {
        headers: {
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        }
      })
        .then((res) => {
          if (!res.ok) throw new Error("Order not found");
          return res.json();
        })
        .then((data) => setOrder(data))
        .catch((err) => setError(err))
        .finally(() => setLoading(false));
    }
  }, [id, order]);

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount).toFixed(2)}`;
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case "pending":
        return { text: "Pending", className: "pending", icon: "⏳" };
      case "processing":
        return { text: "Processing", className: "processing", icon: "🔄" };
      case "completed":
        return { text: "Completed", className: "completed", icon: "✅" };
      default:
        return { text: "Unknown", className: "unknown", icon: "❓" };
    }
  };

  const getTotalItems = (items) => {
    if (!items || !Array.isArray(items)) return 0;
    return items.reduce((total, item) => total + (item.quantity || 0), 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="order-detail-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading order details...</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="order-detail-container">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h2>Order Not Found</h2>
          <p>The requested order could not be found.</p>
          <button 
            className="back-button"
            onClick={() => navigate('/orders')}
          >
            Back to Orders
          </button>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(order.status);
  const totalItems = getTotalItems(order.items);

  return (
    <div className="order-detail-container">
      {/* Breadcrumb Navigation */}
      <nav className="breadcrumb">
        <button 
          className="breadcrumb-link"
          onClick={() => navigate('/orders')}
        >
          Orders
        </button>
        <span className="breadcrumb-separator">›</span>
        <span className="breadcrumb-current">Order #{order.id}</span>
      </nav>

      <div className="order-detail">
        {/* Order Header */}
        <div className="order-header">
          <div className="order-header__info">
            <h1 className="order-title">Order #{order.id}</h1>
            <div className="order-date">{formatDate(order.created_at)}</div>
          </div>
          <div className={`order-status order-status--${statusInfo.className}`}>
            <span className="order-status-icon">{statusInfo.icon}</span>
            <span className="order-status-text">{statusInfo.text}</span>
          </div>
        </div>

        {/* Order Summary */}
        <div className="order-summary">
          <div className="summary-card">
            <h3>Order Summary</h3>
            <div className="summary-details">
              <div className="summary-item">
                <span className="summary-label">Total Items:</span>
                <span className="summary-value">{totalItems}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Total Amount:</span>
                <span className="summary-value summary-value--amount">
                  {formatCurrency(order.total_amount)}
                </span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Status:</span>
                <span className={`summary-value summary-value--status summary-value--${statusInfo.className}`}>
                  {statusInfo.text}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Information */}
        <div className="customer-info">
          <h3>Customer Information</h3>
          <div className="customer-details">
            <div className="customer-item">
              <span className="customer-label">Name:</span>
              <span className="customer-value">{order.customer_name}</span>
            </div>
            <div className="customer-item">
              <span className="customer-label">Email:</span>
              <span className="customer-value">{order.email}</span>
            </div>
            <div className="customer-item">
              <span className="customer-label">Address:</span>
              <span className="customer-value">{order.address}</span>
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="order-items">
          <h3>Order Items</h3>
          {order.items && order.items.length > 0 ? (
            <div className="items-list">
              {order.items.map((item, index) => (
                <div key={index} className="item-card">
                  <div className="item-info">
                    <div className="item-product">
                      <span className="item-label">Product ID:</span>
                      <span className="item-value">{item.product}</span>
                    </div>
                    {item.variation && (
                      <div className="item-variation">
                        <span className="item-label">Variation ID:</span>
                        <span className="item-value">{item.variation}</span>
                      </div>
                    )}
                  </div>
                  <div className="item-details">
                    <div className="item-quantity">
                      <span className="item-label">Quantity:</span>
                      <span className="item-value">{item.quantity}</span>
                    </div>
                    <div className="item-price">
                      <span className="item-label">Price:</span>
                      <span className="item-value item-value--price">
                        {formatCurrency(item.price)}
                      </span>
                    </div>
                    <div className="item-total">
                      <span className="item-label">Total:</span>
                      <span className="item-value item-value--total">
                        {formatCurrency(parseFloat(item.price) * item.quantity)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-items">
              <p>No items found for this order.</p>
            </div>
          )}
        </div>

        {/* Order Actions */}
        <div className="order-actions">
          <button 
            className="action-button action-button--primary"
            onClick={() => navigate('/orders')}
          >
            Back to Orders
          </button>
          <button 
            className="action-button action-button--secondary"
            onClick={() => {
              // Add edit functionality here
              console.log('Edit order:', order.id);
            }}
          >
            Edit Order
          </button>
          <button 
            className="action-button action-button--secondary"
            onClick={() => {
              // Add print functionality here
              window.print();
            }}
          >
            Print Order
          </button>
        </div>
      </div>
    </div>
  );
}

export default OrderDetail;
