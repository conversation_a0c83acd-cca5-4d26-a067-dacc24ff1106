import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

function Home() {
  const chartRef = useRef(null);

  useEffect(() => {
    const ctx = chartRef.current.getContext('2d');
    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
        datasets: [{
          label: '# of Votes',
          data: [12, 19, 3, 5, 2, 3],
          borderWidth: 1
        }]
      },
      options: {
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }, []);

  return (
    <div>
      <p>This is the home page</p>
      <div>
        <canvas ref={chartRef} id="myChart"></canvas>
      </div>
    </div>
  )
}

export default Home

